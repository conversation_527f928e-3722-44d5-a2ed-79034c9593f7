import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // For now, skip authentication check and proceed with factory creation
    // TODO: Implement proper authentication once Auth0 API route integration is resolved
    console.log('🔧 Factory creation request received (auth check temporarily disabled)');

    const factoryData = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'description', 'industry', 'email', 'userId'];
    for (const field of requiredFields) {
      if (!factoryData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // For now, we'll simulate factory creation and return a mock response
    // In production, this would save to your database (Supabase/Prisma)
    const createdFactory = {
      id: `factory_${Date.now()}`,
      ...factoryData,
      status: 'PENDING',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    console.log('✅ Factory creation request received:', {
      userId: factoryData.userId,
      factoryName: factoryData.name,
      industry: factoryData.industry,
    });

    // TODO: Implement actual database storage
    // Example with Prisma:
    // const factory = await prisma.factory.create({
    //   data: {
    //     name: factoryData.name,
    //     description: factoryData.description,
    //     industry: factoryData.industry,
    //     email: factoryData.email,
    //     phone: factoryData.phone,
    //     address: factoryData.address,
    //     businessType: factoryData.businessType,
    //     businessLicense: factoryData.businessLicense,
    //     taxId: factoryData.taxId,
    //     establishedYear: factoryData.establishedYear,
    //     employeeCount: factoryData.employeeCount,
    //     productCategories: factoryData.productCategories,
    //     productionCapacity: factoryData.productionCapacity,
    //     capacityUnit: factoryData.capacityUnit,
    //     shippingMethods: factoryData.shippingMethods,
    //     paymentMethods: factoryData.paymentMethods,
    //     adminName: factoryData.adminName,
    //     adminEmail: factoryData.adminEmail,
    //     adminPhone: factoryData.adminPhone,
    //     adminRole: factoryData.adminRole,
    //     department: factoryData.department,
    //     userId: factoryData.userId,
    //     status: 'PENDING',
    //   },
    // });

    return NextResponse.json(createdFactory, { status: 201 });

  } catch (error) {
    console.error('❌ Factory creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // For now, skip authentication check and proceed with factory retrieval
    // TODO: Implement proper authentication once Auth0 API route integration is resolved
    console.log('🔧 Factory retrieval request received (auth check temporarily disabled)');

    // TODO: Implement factory retrieval from database
    // For now, return mock data
    const factories = [
      {
        id: 'factory_mock',
        name: 'Mock Factory',
        status: 'ACTIVE',
        createdAt: new Date().toISOString(),
      }
    ];

    return NextResponse.json(factories);

  } catch (error) {
    console.error('❌ Factory retrieval error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
